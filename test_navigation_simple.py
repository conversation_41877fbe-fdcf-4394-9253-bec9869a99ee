#!/usr/bin/env python3
"""
Simple navigation test for Smart Supply Management System.
Tests URL resolution and basic functionality without database.
"""

import os
import sys

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SmartSupply.settings')

import django
django.setup()

from django.urls import reverse, NoReverseMatch
from django.template import Template, Context
from django.test import RequestFactory
from suptrack.context_processors import get_admin_navigation_items


def test_url_resolution():
    """Test URL resolution for admin navigation."""
    print("🔗 Testing URL Resolution")
    print("=" * 50)
    
    # Core admin URLs
    admin_urls = [
        'admin_dashboard',
        'admin_users',
        'admin_users_list', 
        'admin_users_add',
        'admin_permissions',
        'admin_roles_management',
        'admin_settings',
        'admin_settings_general',
        'admin_settings_email',
        'admin_settings_backup',
        'admin_settings_security',
        'admin_inventory',
        'admin_requests',
        'admin_bulk_operations',
        'admin_notifications',
        'admin_notifications_list',
        'create_system_notification',
        'admin_notification_templates',
        'admin_notification_settings',
        'admin_system_health',
        'admin_audit_logs',
        'admin_performance_reports'
    ]
    
    resolved = []
    failed = []
    
    for url_name in admin_urls:
        try:
            url = reverse(url_name)
            resolved.append((url_name, url))
            print(f"✅ {url_name}: {url}")
        except NoReverseMatch as e:
            failed.append((url_name, str(e)))
            print(f"❌ {url_name}: {e}")
    
    print(f"\n📊 Results: {len(resolved)} resolved, {len(failed)} failed")
    return len(resolved), len(failed)


def test_navigation_items():
    """Test admin navigation items generation."""
    print("\n🧭 Testing Navigation Items Generation")
    print("=" * 50)
    
    try:
        admin_items = get_admin_navigation_items()
        
        print(f"✅ Generated {len(admin_items)} navigation items")
        
        for item in admin_items:
            name = item.get('name', 'Unknown')
            url = item.get('url', 'No URL')
            section = item.get('section', 'No Section')
            
            print(f"  📁 {name} ({section}): {url}")
            
            # Check for submenu
            if 'submenu' in item:
                submenu = item['submenu']
                print(f"    └── {len(submenu)} submenu items")
                for subitem in submenu:
                    subname = subitem.get('name', 'Unknown')
                    suburl = subitem.get('url', 'No URL')
                    print(f"      └── {subname}: {suburl}")
        
        return True, len(admin_items)
        
    except Exception as e:
        print(f"❌ Error generating navigation items: {e}")
        return False, 0


def test_template_syntax():
    """Test admin sidebar template syntax."""
    print("\n📄 Testing Template Syntax")
    print("=" * 50)
    
    try:
        # Read the admin sidebar template
        with open('templates/partials/admin_sidebar_nav.html', 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # Try to compile the template
        template = Template(template_content)
        print("✅ Template syntax is valid")
        
        # Test basic rendering (without full context)
        try:
            context = Context({})
            rendered = template.render(context)
            print("✅ Template renders without context errors")
            return True
        except Exception as e:
            print(f"⚠️  Template rendering issue: {e}")
            return True  # Syntax is still valid
            
    except Exception as e:
        print(f"❌ Template syntax error: {e}")
        return False


def test_htmx_urls():
    """Test HTMX endpoint URLs."""
    print("\n⚡ Testing HTMX Endpoints")
    print("=" * 50)
    
    htmx_urls = [
        'admin_users_count',
        'admin_inventory_alerts_count', 
        'admin_pending_requests_count',
        'admin_notification_alerts_count',
        'admin_audit_alerts_count',
        'admin_system_health_indicator',
        'admin_quick_stats_htmx'
    ]
    
    resolved = []
    failed = []
    
    for url_name in htmx_urls:
        try:
            url = reverse(url_name)
            resolved.append((url_name, url))
            print(f"✅ {url_name}: {url}")
        except NoReverseMatch as e:
            failed.append((url_name, str(e)))
            print(f"❌ {url_name}: {e}")
    
    print(f"\n📊 HTMX Results: {len(resolved)} resolved, {len(failed)} failed")
    return len(resolved), len(failed)


def test_css_js_files():
    """Test if CSS and JS files exist."""
    print("\n🎨 Testing Static Files")
    print("=" * 50)
    
    static_files = [
        'static/css/admin_navigation.css',
        'static/js/admin_navigation.js'
    ]
    
    existing = []
    missing = []
    
    for file_path in static_files:
        if os.path.exists(file_path):
            existing.append(file_path)
            print(f"✅ {file_path}")
        else:
            missing.append(file_path)
            print(f"❌ {file_path} (missing)")
    
    print(f"\n📊 Static Files: {len(existing)} found, {len(missing)} missing")
    return len(existing), len(missing)


def main():
    """Run all navigation tests."""
    print("🚀 Smart Supply - Navigation Test Suite")
    print("=" * 60)
    
    results = []
    
    # Test URL resolution
    resolved_count, failed_count = test_url_resolution()
    results.append(('URL Resolution', resolved_count > failed_count))
    
    # Test navigation items
    nav_success, nav_count = test_navigation_items()
    results.append(('Navigation Items', nav_success and nav_count > 0))
    
    # Test template syntax
    template_success = test_template_syntax()
    results.append(('Template Syntax', template_success))
    
    # Test HTMX URLs
    htmx_resolved, htmx_failed = test_htmx_urls()
    results.append(('HTMX Endpoints', htmx_resolved >= htmx_failed))
    
    # Test static files
    static_found, static_missing = test_css_js_files()
    results.append(('Static Files', static_found > 0))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All navigation tests passed!")
        return True
    elif passed >= total * 0.8:  # 80% pass rate
        print("✅ Most navigation tests passed!")
        return True
    else:
        print("⚠️  Some critical navigation issues detected.")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
