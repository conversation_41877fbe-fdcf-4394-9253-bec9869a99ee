/**
 * Admin Navigation Management System
 * Handles sidebar navigation state, active highlighting, and keyboard shortcuts for admin users
 */

class AdminNavigation {
    constructor() {
        this.currentPath = window.location.pathname;
        this.sidebarState = this.loadSidebarState();
        this.init();
    }

    /**
     * Initialize the navigation system
     */
    init() {
        this.setupEventListeners();
        this.highlightActiveNavigation();
        this.restoreSidebarState();
        this.setupKeyboardShortcuts();
        this.initializeBadges();
        
        console.log('Admin Navigation initialized');
    }

    /**
     * Setup event listeners for navigation interactions
     */
    setupEventListeners() {
        // Handle navigation link clicks for dropdown toggle
        document.addEventListener('click', (e) => {
            const navSection = e.target.closest('.nav-section');
            if (navSection && e.target.closest('.nav-link')) {
                const submenu = navSection.querySelector('.nav-submenu');
                if (submenu) {
                    e.preventDefault();
                    this.toggleSubmenu(navSection);
                    return;
                }
            }

            // Handle regular navigation link clicks
            const navLink = e.target.closest('.nav-link');
            if (navLink && !navLink.closest('.nav-section')) {
                this.handleNavLinkClick(navLink, e);
            }
        });

        // Save state on page unload
        window.addEventListener('beforeunload', () => {
            this.saveSidebarState();
        });

        // Handle HTMX events for badge updates
        document.body.addEventListener('htmx:afterRequest', (e) => {
            if (e.target.id && e.target.id.includes('badge')) {
                this.updateBadgeDisplay(e.target);
            }
        });
    }

    /**
     * Handle navigation link clicks (for non-dropdown links)
     */
    handleNavLinkClick(navLink, event) {
        const navItem = navLink.getAttribute('data-nav-item');

        // Track navigation for analytics
        this.trackNavigation(navItem, navLink.href);

        // Update active state
        this.setActiveNavigation(navLink);
    }

    /**
     * Toggle submenu visibility
     */
    toggleSubmenu(navSection) {
        const submenu = navSection.querySelector('.nav-submenu');
        const chevron = navSection.querySelector('.nav-link svg:last-child');
        const sectionName = navSection.getAttribute('data-nav-section');
        
        if (!submenu) return;
        
        const isHidden = submenu.classList.contains('hidden');
        
        if (isHidden) {
            // Show submenu
            submenu.classList.remove('hidden');
            chevron?.classList.add('rotate-180');
            this.sidebarState.openSections.add(sectionName);
        } else {
            // Hide submenu
            submenu.classList.add('hidden');
            chevron?.classList.remove('rotate-180');
            this.sidebarState.openSections.delete(sectionName);
        }
        
        this.saveSidebarState();
    }

    /**
     * Highlight active navigation based on current path
     */
    highlightActiveNavigation() {
        // Remove all active states
        document.querySelectorAll('.nav-link, .nav-sublink').forEach(link => {
            link.classList.remove('active', 'bg-blue-100', 'text-blue-700', 'border-r-2', 'border-blue-500');
        });
        
        // Find and highlight active navigation
        const activeLink = this.findActiveNavigation();
        if (activeLink) {
            this.setActiveNavigation(activeLink);
        }
    }

    /**
     * Find the active navigation link based on current path
     */
    findActiveNavigation() {
        const links = document.querySelectorAll('.nav-link, .nav-sublink');
        let bestMatch = null;
        let bestMatchLength = 0;
        
        for (const link of links) {
            const href = link.getAttribute('href');
            if (href && this.isPathMatch(href)) {
                if (href.length > bestMatchLength) {
                    bestMatch = link;
                    bestMatchLength = href.length;
                }
            }
        }
        
        return bestMatch;
    }

    /**
     * Check if current path matches the given href
     */
    isPathMatch(href) {
        // Exact match first
        if (this.currentPath === href) return true;
        
        // For admin paths, check if current path starts with href
        if (href !== '/admin-dashboard/' && this.currentPath.startsWith(href)) return true;

        // Handle special cases for nested routes (updated for admin-panel)
        if (href.includes('/admin-panel/') && this.currentPath.includes('/admin-panel/')) {
            const hrefBase = href.split('?')[0].split('#')[0];
            const currentBase = this.currentPath.split('?')[0].split('#')[0];

            if (currentBase.startsWith(hrefBase)) return true;
        }

        // Legacy admin path support (for backward compatibility)
        if (href.includes('/admin/') && this.currentPath.includes('/admin-panel/')) {
            const hrefBase = href.replace('/admin/', '/admin-panel/').split('?')[0].split('#')[0];
            const currentBase = this.currentPath.split('?')[0].split('#')[0];

            if (currentBase.startsWith(hrefBase)) return true;
        }
        
        return false;
    }

    /**
     * Set active state for navigation link
     */
    setActiveNavigation(link) {
        // Add active styling
        link.classList.add('active', 'bg-blue-100', 'text-blue-700');
        
        // If it's a sublink, also open its parent section
        if (link.classList.contains('nav-sublink')) {
            const parentSection = link.closest('.nav-section');
            if (parentSection) {
                const parentSubmenu = parentSection.querySelector('.nav-submenu');
                const parentChevron = parentSection.querySelector('.nav-link svg:last-child');
                
                if (parentSubmenu) {
                    parentSubmenu.classList.remove('hidden');
                    parentChevron?.classList.add('rotate-180');
                    
                    const sectionName = parentSection.getAttribute('data-nav-section');
                    this.sidebarState.openSections.add(sectionName);
                }
            }
        }
    }

    /**
     * Restore sidebar state from localStorage
     */
    restoreSidebarState() {
        // Restore open sections
        this.sidebarState.openSections.forEach(sectionName => {
            const section = document.querySelector(`[data-nav-section="${sectionName}"]`);
            if (section) {
                const submenu = section.querySelector('.nav-submenu');
                const chevron = section.querySelector('.nav-link svg:last-child');
                
                if (submenu) {
                    submenu.classList.remove('hidden');
                    chevron?.classList.add('rotate-180');
                }
            }
        });
    }

    /**
     * Load sidebar state from localStorage
     */
    loadSidebarState() {
        try {
            const saved = localStorage.getItem('adminSidebarState');
            if (saved) {
                const parsed = JSON.parse(saved);
                return {
                    openSections: new Set(parsed.openSections || [])
                };
            }
        } catch (e) {
            console.warn('Failed to load sidebar state:', e);
        }
        
        return {
            openSections: new Set()
        };
    }

    /**
     * Save sidebar state to localStorage
     */
    saveSidebarState() {
        try {
            const state = {
                openSections: Array.from(this.sidebarState.openSections)
            };
            localStorage.setItem('adminSidebarState', JSON.stringify(state));
        } catch (e) {
            console.warn('Failed to save sidebar state:', e);
        }
    }

    /**
     * Setup keyboard shortcuts for admin navigation
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Only handle shortcuts when not in input fields
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                return;
            }
            
            // Alt + key combinations for quick navigation
            if (e.altKey && !e.ctrlKey && !e.shiftKey) {
                switch (e.key.toLowerCase()) {
                    case 'd':
                        e.preventDefault();
                        this.navigateToUrl('dashboard');
                        break;
                    case 'u':
                        e.preventDefault();
                        this.navigateToUrl('users');
                        break;
                    case 's':
                        e.preventDefault();
                        this.navigateToUrl('settings');
                        break;
                    case 'i':
                        e.preventDefault();
                        this.navigateToUrl('inventory');
                        break;
                    case 'r':
                        e.preventDefault();
                        this.navigateToUrl('requests');
                        break;
                    case 'a':
                        e.preventDefault();
                        this.navigateToUrl('reports');
                        break;
                    case 'l':
                        e.preventDefault();
                        this.navigateToUrl('audit-logs');
                        break;
                    case 'n':
                        e.preventDefault();
                        this.navigateToUrl('notifications');
                        break;
                }
            }
        });
    }

    /**
     * Navigate to a URL by name using Django URL resolution
     */
    navigateToUrl(urlName) {
        // Try to find the URL from existing navigation links first
        const navLink = document.querySelector(`[data-nav-item="${urlName}"]`);
        if (navLink && navLink.href) {
            window.location.href = navLink.href;
            return;
        }
        
        console.warn(`No URL found for navigation item: ${urlName}`);
    }

    /**
     * Initialize badge loading and updates
     */
    initializeBadges() {
        // Force initial load of all badges
        const badges = document.querySelectorAll('[id$="-badge"], [id$="-indicator"]');
        badges.forEach(badge => {
            if (badge.hasAttribute('hx-get')) {
                htmx.trigger(badge, 'load');
            }
        });
    }

    /**
     * Update badge display after HTMX response
     */
    updateBadgeDisplay(badgeElement) {
        // Add animation class for visual feedback
        badgeElement.classList.add('animate-pulse');
        setTimeout(() => {
            badgeElement.classList.remove('animate-pulse');
        }, 1000);
    }

    /**
     * Track navigation for analytics
     */
    trackNavigation(navItem, url) {
        // Log navigation event (can be extended for analytics)
        console.log(`Admin navigation: ${navItem} -> ${url}`);
        
        // Could send to analytics service here
        // analytics.track('admin_navigation', { item: navItem, url: url });
    }

    /**
     * Get current navigation state
     */
    getState() {
        const state = {
            currentPath: this.currentPath,
            activeLinks: [],
            openSections: Array.from(this.sidebarState.openSections),
            breadcrumbs: this.generateBreadcrumbs()
        };
        
        // Get active links
        document.querySelectorAll('.nav-link.active, .nav-sublink.active').forEach(link => {
            state.activeLinks.push({
                href: link.getAttribute('href'),
                text: link.textContent.trim(),
                isSublink: link.classList.contains('nav-sublink')
            });
        });
        
        return state;
    }

    /**
     * Generate breadcrumbs based on current path
     */
    generateBreadcrumbs() {
        const breadcrumbs = [
            { name: 'Admin', url: '/admin-dashboard/' }
        ];
        
        const pathSegments = this.currentPath.split('/').filter(segment => segment);
        
        if (pathSegments.length > 1) {
            const section = pathSegments[1];
            
            switch (section) {
                case 'admin-dashboard':
                    breadcrumbs.push({ name: 'Dashboard', url: null });
                    break;
                case 'admin-panel':
                    if (pathSegments.length > 2) {
                        const subsection = pathSegments[2];
                        switch (subsection) {
                            case 'users':
                                breadcrumbs.push({ name: 'User Management', url: '/admin-panel/users/' });
                                break;
                            case 'settings':
                                breadcrumbs.push({ name: 'System Settings', url: '/admin-panel/settings/' });
                                break;
                            case 'inventory':
                                breadcrumbs.push({ name: 'Inventory Management', url: '/admin-panel/inventory/' });
                                break;
                            case 'requests':
                                breadcrumbs.push({ name: 'Request Management', url: '/admin-panel/requests/' });
                                break;
                            case 'audit-logs':
                                breadcrumbs.push({ name: 'Audit Logs', url: '/admin-panel/audit-logs/' });
                                break;
                            case 'notifications':
                                breadcrumbs.push({ name: 'Notifications', url: '/admin-panel/notifications/' });
                                break;
                            case 'system-health':
                                breadcrumbs.push({ name: 'System Health', url: '/admin-panel/system-health/' });
                                break;
                        }
                    }
                    break;
                case 'reports':
                    breadcrumbs.push({ name: 'Reports & Analytics', url: '/reports/' });
                    break;
                default:
                    breadcrumbs.push({ name: 'Unknown', url: null });
            }
        }
        
        return breadcrumbs;
    }
}

// Initialize admin navigation when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.adminNavigation = new AdminNavigation();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdminNavigation;
}
