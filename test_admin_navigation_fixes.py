#!/usr/bin/env python3
"""
Test script to verify admin navigation fixes.
Tests URL resolution, routing conflicts, and navigation functionality.
"""

import os
import sys
import requests
from urllib.parse import urljoin

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SmartSupply.settings')

import django
django.setup()

from django.urls import reverse, NoReverseMatch
from django.test import Client
from django.contrib.auth.models import User
from suptrack.models import UserProfile
from suptrack.context_processors import get_admin_navigation_items


def test_url_resolution():
    """Test that all admin URLs resolve correctly."""
    print("🔗 Testing URL Resolution")
    print("=" * 50)
    
    admin_urls = [
        'admin_dashboard',
        'admin_users',
        'admin_users_list',
        'admin_users_add',
        'admin_permissions',
        'admin_roles_management',
        'admin_settings',
        'admin_settings_general',
        'admin_settings_email',
        'admin_settings_backup',
        'admin_settings_security',
        'admin_inventory',
        'admin_requests',
        'admin_bulk_operations',
        'admin_notifications',
        'admin_notifications_list',
        'create_system_notification',
        'admin_notification_templates',
        'admin_notification_settings',
        'admin_system_health',
        'admin_audit_logs',
        'admin_performance_reports'
    ]
    
    resolved = []
    failed = []
    
    for url_name in admin_urls:
        try:
            url = reverse(url_name)
            resolved.append((url_name, url))
            print(f"✅ {url_name}: {url}")
        except NoReverseMatch as e:
            failed.append((url_name, str(e)))
            print(f"❌ {url_name}: {e}")
    
    print(f"\n📊 Results: {len(resolved)} resolved, {len(failed)} failed")
    return len(resolved), len(failed)


def test_django_admin_conflict():
    """Test that Django admin and custom admin don't conflict."""
    print("\n🔧 Testing Django Admin Conflict Resolution")
    print("=" * 50)
    
    # Test Django admin URL
    try:
        django_admin_url = "/admin/"
        print(f"✅ Django Admin URL: {django_admin_url}")
        django_admin_ok = True
    except Exception as e:
        print(f"❌ Django Admin URL error: {e}")
        django_admin_ok = False
    
    # Test custom admin URLs
    try:
        custom_admin_urls = [
            reverse('admin_users'),
            reverse('admin_settings'),
            reverse('admin_notifications')
        ]
        print(f"✅ Custom Admin URLs: {custom_admin_urls}")
        custom_admin_ok = True
    except Exception as e:
        print(f"❌ Custom Admin URLs error: {e}")
        custom_admin_ok = False
    
    # Check for conflicts
    conflicts = []
    for url_name in ['admin_users', 'admin_settings']:
        try:
            url = reverse(url_name)
            if url.startswith('/admin/') and not url.startswith('/admin-panel/'):
                conflicts.append((url_name, url))
        except:
            pass
    
    if conflicts:
        print(f"⚠️  URL Conflicts detected: {conflicts}")
        conflict_free = False
    else:
        print("✅ No URL conflicts detected")
        conflict_free = True
    
    return django_admin_ok and custom_admin_ok and conflict_free


def test_navigation_items():
    """Test navigation items generation."""
    print("\n🧭 Testing Navigation Items Generation")
    print("=" * 50)
    
    try:
        admin_items = get_admin_navigation_items()
        
        print(f"✅ Generated {len(admin_items)} navigation items")
        
        # Check for required sections
        expected_sections = [
            'Dashboard',
            'User Management',
            'System Settings',
            'Request Management',
            'Notifications'
        ]
        
        found_sections = [item['name'] for item in admin_items]
        missing_sections = [s for s in expected_sections if s not in found_sections]
        
        if missing_sections:
            print(f"⚠️  Missing sections: {missing_sections}")
        else:
            print("✅ All expected sections found")
        
        # Test submenu structure
        submenu_count = 0
        for item in admin_items:
            if 'submenu' in item:
                submenu_count += len(item['submenu'])
                print(f"  📁 {item['name']}: {len(item['submenu'])} submenu items")
        
        print(f"✅ Total submenu items: {submenu_count}")
        
        return True, len(admin_items), submenu_count
        
    except Exception as e:
        print(f"❌ Error generating navigation items: {e}")
        return False, 0, 0


def test_htmx_endpoints():
    """Test HTMX endpoint URLs."""
    print("\n⚡ Testing HTMX Endpoints")
    print("=" * 50)
    
    htmx_urls = [
        'admin_users_count',
        'admin_inventory_alerts_count',
        'admin_pending_requests_count',
        'admin_notification_alerts_count',
        'admin_audit_alerts_count',
        'admin_system_health_indicator',
        'admin_quick_stats_htmx'
    ]
    
    resolved = []
    failed = []
    
    for url_name in htmx_urls:
        try:
            url = reverse(url_name)
            resolved.append((url_name, url))
            print(f"✅ {url_name}: {url}")
        except NoReverseMatch as e:
            failed.append((url_name, str(e)))
            print(f"❌ {url_name}: {e}")
    
    print(f"\n📊 HTMX Results: {len(resolved)} resolved, {len(failed)} failed")
    return len(resolved), len(failed)


def test_server_accessibility():
    """Test that admin pages are accessible via HTTP."""
    print("\n🌐 Testing Server Accessibility")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    test_urls = [
        "/admin-dashboard/",
        "/admin-panel/users/",
        "/admin-panel/settings/",
        "/admin/",  # Django admin
    ]
    
    accessible = []
    inaccessible = []
    
    for url in test_urls:
        try:
            full_url = urljoin(base_url, url)
            response = requests.get(full_url, timeout=5, allow_redirects=True)
            
            if response.status_code in [200, 302, 403]:  # 403 is OK (access control)
                accessible.append((url, response.status_code))
                print(f"✅ {url}: HTTP {response.status_code}")
            else:
                inaccessible.append((url, response.status_code))
                print(f"❌ {url}: HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            inaccessible.append((url, str(e)))
            print(f"❌ {url}: {e}")
    
    print(f"\n📊 Accessibility: {len(accessible)} accessible, {len(inaccessible)} inaccessible")
    return len(accessible), len(inaccessible)


def test_static_files():
    """Test that static files exist."""
    print("\n🎨 Testing Static Files")
    print("=" * 50)
    
    static_files = [
        'static/css/admin_navigation.css',
        'static/js/admin_navigation.js'
    ]
    
    existing = []
    missing = []
    
    for file_path in static_files:
        if os.path.exists(file_path):
            existing.append(file_path)
            print(f"✅ {file_path}")
        else:
            missing.append(file_path)
            print(f"❌ {file_path} (missing)")
    
    print(f"\n📊 Static Files: {len(existing)} found, {len(missing)} missing")
    return len(existing), len(missing)


def main():
    """Run all navigation fix tests."""
    print("🚀 Admin Navigation Fixes - Comprehensive Test Suite")
    print("=" * 70)
    
    results = []
    
    # Test URL resolution
    resolved_count, failed_count = test_url_resolution()
    results.append(('URL Resolution', resolved_count > failed_count and failed_count == 0))
    
    # Test Django admin conflict resolution
    conflict_resolved = test_django_admin_conflict()
    results.append(('Django Admin Conflict Resolution', conflict_resolved))
    
    # Test navigation items
    nav_success, nav_count, submenu_count = test_navigation_items()
    results.append(('Navigation Items Generation', nav_success and nav_count > 0))
    
    # Test HTMX endpoints
    htmx_resolved, htmx_failed = test_htmx_endpoints()
    results.append(('HTMX Endpoints', htmx_resolved > htmx_failed))
    
    # Test server accessibility
    accessible_count, inaccessible_count = test_server_accessibility()
    results.append(('Server Accessibility', accessible_count > inaccessible_count))
    
    # Test static files
    static_found, static_missing = test_static_files()
    results.append(('Static Files', static_found > 0 and static_missing == 0))
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 COMPREHENSIVE TEST SUMMARY")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All admin navigation fixes verified successfully!")
        print("\n✅ Key Achievements:")
        print("   • URL conflicts with Django admin resolved")
        print("   • All admin navigation URLs working")
        print("   • HTMX endpoints functional")
        print("   • Static files in place")
        print("   • Server accessibility confirmed")
        return True
    else:
        print("⚠️  Some issues remain. Check the output above for details.")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
