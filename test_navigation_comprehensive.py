#!/usr/bin/env python3
"""
Comprehensive navigation testing for Smart Supply Management System.
Tests admin sidebar navigation, URL resolution, and error handling.
"""

import os
import sys
import json

# Setup Django first
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SmartSupply.settings')

import django
django.setup()

from django.test import TestCase, Client, RequestFactory
from django.contrib.auth.models import User
from django.urls import reverse, NoReverseMatch
from django.http import HttpResponse
from django.template import Context, Template

from suptrack.models import UserProfile, SupplyItem, SupplyRequest
from suptrack.context_processors import (
    navigation_context, get_admin_navigation_items, get_gso_navigation_items,
    generate_admin_breadcrumbs, generate_gso_breadcrumbs
)


class NavigationTestCase(TestCase):
    """Comprehensive test suite for navigation functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.factory = RequestFactory()
        
        # Create test users
        self.admin_user = User.objects.create_user(
            username='admin_test',
            password='testpass123',
            email='<EMAIL>'
        )
        UserProfile.objects.create(user=self.admin_user, role='admin')
        
        self.gso_user = User.objects.create_user(
            username='gso_test',
            password='testpass123',
            email='<EMAIL>'
        )
        UserProfile.objects.create(user=self.gso_user, role='gso_staff')
        
        self.regular_user = User.objects.create_user(
            username='user_test',
            password='testpass123',
            email='<EMAIL>'
        )
        UserProfile.objects.create(user=self.regular_user, role='department_user')
    
    def test_admin_navigation_url_resolution(self):
        """Test that all admin navigation URLs can be resolved."""
        print("\n=== Testing Admin Navigation URL Resolution ===")
        
        admin_routes = [
            'admin_dashboard',
            'admin_users',
            'admin_users_list',
            'admin_users_add',
            'admin_roles_management',
            'admin_permissions',
            'admin_settings',
            'admin_settings_general',
            'admin_settings_email',
            'admin_settings_backup',
            'admin_settings_security',
            'admin_inventory',
            'admin_requests',
            'admin_bulk_operations',
            'admin_performance_reports',
            'admin_audit_logs',
            'admin_notifications',
            'admin_notifications_list',
            'create_system_notification',
            'admin_notification_templates',
            'admin_notification_settings',
            'admin_system_health'
        ]
        
        success_count = 0
        failed_routes = []
        
        for route_name in admin_routes:
            try:
                url = reverse(route_name)
                print(f"✅ {route_name}: {url}")
                success_count += 1
            except NoReverseMatch as e:
                print(f"❌ {route_name}: {e}")
                failed_routes.append(route_name)
        
        print(f"\nURL Resolution: {success_count}/{len(admin_routes)} successful")
        
        if failed_routes:
            print(f"Failed routes: {failed_routes}")
        
        return success_count, failed_routes
    
    def test_admin_navigation_context_processor(self):
        """Test admin navigation context processor."""
        print("\n=== Testing Admin Navigation Context Processor ===")
        
        # Test admin navigation items generation
        try:
            admin_items = get_admin_navigation_items()
            print(f"✅ Admin navigation items generated: {len(admin_items)} items")
            
            # Check if main sections exist
            expected_sections = ['Dashboard', 'User Management', 'System Settings', 
                               'Inventory Management', 'Request Management', 
                               'Reports & Analytics', 'Audit Logs', 'Notifications', 
                               'System Health']
            
            found_sections = [item['name'] for item in admin_items]
            missing_sections = [section for section in expected_sections if section not in found_sections]
            
            if missing_sections:
                print(f"⚠️  Missing sections: {missing_sections}")
            else:
                print("✅ All expected sections found")
            
            return True, len(admin_items), missing_sections
            
        except Exception as e:
            print(f"❌ Error generating admin navigation: {e}")
            return False, 0, []
    
    def test_admin_routes_accessibility(self):
        """Test admin routes accessibility with proper authentication."""
        print("\n=== Testing Admin Routes Accessibility ===")
        
        # Login as admin
        self.client.login(username='admin_test', password='testpass123')
        
        test_routes = [
            ('admin_dashboard', 'Admin Dashboard'),
            ('admin_users_list', 'User Management'),
            ('admin_settings', 'System Settings'),
            ('admin_requests', 'Request Management'),
            ('admin_notifications', 'Notifications'),
            ('admin_system_health', 'System Health')
        ]
        
        success_count = 0
        failed_routes = []
        
        for route_name, description in test_routes:
            try:
                url = reverse(route_name)
                response = self.client.get(url)
                
                if response.status_code in [200, 302]:
                    print(f"✅ {description} ({route_name}): {response.status_code}")
                    success_count += 1
                else:
                    print(f"❌ {description} ({route_name}): {response.status_code}")
                    failed_routes.append((route_name, response.status_code))
                    
            except NoReverseMatch:
                print(f"❌ {description} ({route_name}): URL not found")
                failed_routes.append((route_name, 'NoReverseMatch'))
            except Exception as e:
                print(f"❌ {description} ({route_name}): {e}")
                failed_routes.append((route_name, str(e)))
        
        print(f"\nRoute Accessibility: {success_count}/{len(test_routes)} successful")
        return success_count, failed_routes
    
    def test_access_control(self):
        """Test access control for admin routes."""
        print("\n=== Testing Access Control ===")
        
        admin_url = reverse('admin_users_list')
        
        # Test unauthenticated access
        response = self.client.get(admin_url)
        print(f"Unauthenticated access: {response.status_code}")
        unauthenticated_ok = response.status_code in [302, 403]
        
        # Test regular user access
        self.client.login(username='user_test', password='testpass123')
        response = self.client.get(admin_url)
        print(f"Regular user access: {response.status_code}")
        regular_user_denied = response.status_code in [302, 403]
        
        # Test GSO user access
        self.client.login(username='gso_test', password='testpass123')
        response = self.client.get(admin_url)
        print(f"GSO user access: {response.status_code}")
        gso_user_denied = response.status_code in [302, 403]
        
        # Test admin user access
        self.client.login(username='admin_test', password='testpass123')
        response = self.client.get(admin_url)
        print(f"Admin user access: {response.status_code}")
        admin_user_allowed = response.status_code in [200, 302]
        
        access_control_working = (unauthenticated_ok and regular_user_denied and 
                                gso_user_denied and admin_user_allowed)
        
        if access_control_working:
            print("✅ Access control working correctly")
        else:
            print("❌ Access control issues detected")
        
        return access_control_working
    
    def test_breadcrumb_generation(self):
        """Test breadcrumb generation for admin paths."""
        print("\n=== Testing Breadcrumb Generation ===")
        
        test_paths = [
            ('/admin-dashboard/', 'Admin Dashboard'),
            ('/admin/users/', 'User Management'),
            ('/admin/users/add/', 'Add User'),
            ('/admin/settings/', 'System Settings'),
            ('/admin/settings/general/', 'General Settings'),
            ('/admin/notifications/', 'Notifications'),
            ('/admin/notifications/create/', 'Create Notification')
        ]
        
        success_count = 0
        
        for path, description in test_paths:
            try:
                breadcrumbs = generate_admin_breadcrumbs(path)
                if breadcrumbs:
                    print(f"✅ {description}: {len(breadcrumbs)} breadcrumbs")
                    success_count += 1
                else:
                    print(f"⚠️  {description}: No breadcrumbs generated")
            except Exception as e:
                print(f"❌ {description}: Error - {e}")
        
        print(f"\nBreadcrumb generation: {success_count}/{len(test_paths)} successful")
        return success_count
    
    def test_navigation_context_processor(self):
        """Test the navigation context processor with different user roles."""
        print("\n=== Testing Navigation Context Processor ===")
        
        test_cases = [
            (self.admin_user, 'admin', 'Admin navigation'),
            (self.gso_user, 'gso_staff', 'GSO navigation'),
            (self.regular_user, 'department_user', 'User navigation')
        ]
        
        success_count = 0
        
        for user, expected_role, description in test_cases:
            try:
                request = self.factory.get('/test/')
                request.user = user
                
                context = navigation_context(request)
                
                if context['user_role'] == expected_role:
                    print(f"✅ {description}: Role correctly identified as {expected_role}")
                    success_count += 1
                else:
                    print(f"❌ {description}: Expected {expected_role}, got {context['user_role']}")
                
                # Check if navigation items are generated
                if context['navigation_items']:
                    print(f"   Navigation items: {len(context['navigation_items'])}")
                else:
                    print(f"   No navigation items generated")
                    
            except Exception as e:
                print(f"❌ {description}: Error - {e}")
        
        print(f"\nContext processor: {success_count}/{len(test_cases)} successful")
        return success_count
    
    def test_template_rendering(self):
        """Test that admin templates render without errors."""
        print("\n=== Testing Template Rendering ===")
        
        self.client.login(username='admin_test', password='testpass123')
        
        template_routes = [
            ('admin_dashboard', 'Admin Dashboard'),
            ('admin_users_list', 'User List'),
            ('admin_users_add', 'Add User'),
            ('admin_settings', 'System Settings')
        ]
        
        success_count = 0
        
        for route_name, description in template_routes:
            try:
                url = reverse(route_name)
                response = self.client.get(url)
                
                if response.status_code == 200:
                    content = response.content.decode('utf-8')
                    if 'Smart Supply' in content:
                        print(f"✅ {description}: Template renders correctly")
                        success_count += 1
                    else:
                        print(f"⚠️  {description}: Template missing expected content")
                else:
                    print(f"❌ {description}: HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"❌ {description}: Error - {e}")
        
        print(f"\nTemplate rendering: {success_count}/{len(template_routes)} successful")
        return success_count
    
    def run_all_tests(self):
        """Run all navigation tests."""
        print("🚀 Starting Comprehensive Navigation Tests")
        print("=" * 60)
        
        test_results = []
        
        # Run all test methods
        test_methods = [
            ('URL Resolution', self.test_admin_navigation_url_resolution),
            ('Context Processor', self.test_admin_navigation_context_processor),
            ('Route Accessibility', self.test_admin_routes_accessibility),
            ('Access Control', self.test_access_control),
            ('Breadcrumb Generation', self.test_breadcrumb_generation),
            ('Navigation Context', self.test_navigation_context_processor),
            ('Template Rendering', self.test_template_rendering)
        ]
        
        for test_name, test_method in test_methods:
            try:
                result = test_method()
                if isinstance(result, tuple):
                    success = result[0] > 0 if isinstance(result[0], int) else result[0]
                else:
                    success = result
                test_results.append((test_name, success))
            except Exception as e:
                print(f"❌ {test_name}: Exception - {e}")
                test_results.append((test_name, False))
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for _, result in test_results if result)
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
        
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All navigation tests passed!")
            return True
        else:
            print("⚠️  Some tests failed. Check the output above for details.")
            return False


def main():
    """Main test runner."""
    print("Smart Supply - Comprehensive Navigation Test Suite")
    print("=" * 60)
    
    # Create test instance
    test_suite = NavigationTestCase()
    test_suite.setUp()
    
    # Run tests
    success = test_suite.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
