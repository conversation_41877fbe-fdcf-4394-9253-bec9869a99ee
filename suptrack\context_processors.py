"""
Context processors for the Smart Supply Management System.
These processors provide common context data across all templates.
"""

from django.urls import reverse, NoReverseMatch
from django.contrib.auth.models import AnonymousUser
from django.db import models
from django.utils import timezone
from .models import SupplyRequest, SupplyItem, UserProfile
from .decorators import get_user_role
import logging

logger = logging.getLogger(__name__)


def navigation_context(request):
    """
    Provides navigation-related context data for all templates.
    
    Args:
        request: Django request object
    
    Returns:
        dict: Context data for navigation
    """
    context = {
        'current_path': request.path,
        'current_url_name': None,
        'user_role': None,
        'navigation_items': [],
        'breadcrumbs': [],
    }
    
    # Get current URL name
    try:
        from django.urls import resolve
        url_match = resolve(request.path)
        context['current_url_name'] = url_match.url_name
    except:
        pass
    
    # Get user role if authenticated
    if request.user.is_authenticated and not isinstance(request.user, AnonymousUser):
        context['user_role'] = get_user_role(request.user)
        
        # Generate navigation items based on user role
        if context['user_role'] == 'gso_staff':
            context['navigation_items'] = get_gso_navigation_items()
            context['breadcrumbs'] = generate_gso_breadcrumbs(request.path)
        elif context['user_role'] == 'admin':
            context['navigation_items'] = get_admin_navigation_items()
            context['breadcrumbs'] = generate_admin_breadcrumbs(request.path)
        else:
            context['navigation_items'] = get_user_navigation_items()
            context['breadcrumbs'] = generate_user_breadcrumbs(request.path)
    
    return context


def gso_context(request):
    """
    Provides GSO-specific context data for GSO templates.
    
    Args:
        request: Django request object
    
    Returns:
        dict: Context data for GSO views
    """
    context = {}
    
    # Only provide GSO context for authenticated GSO staff
    if (request.user.is_authenticated and 
        not isinstance(request.user, AnonymousUser) and 
        get_user_role(request.user) == 'gso_staff'):
        
        try:
            # Quick stats for GSO dashboard
            context.update({
                'pending_requests_count': SupplyRequest.objects.filter(status='pending').count(),
                'low_stock_items_count': SupplyItem.objects.filter(
                    current_stock__lte=models.F('minimum_stock')
                ).count(),
                'total_items_count': SupplyItem.objects.count(),
                'recent_requests_count': SupplyRequest.objects.filter(
                    request_date__gte=timezone.now() - timezone.timedelta(days=7)
                ).count(),
            })
        except Exception as e:
            logger.warning(f"Error generating GSO context: {e}")
    
    return context


def get_gso_navigation_items():
    """
    Returns navigation items for GSO staff.
    
    Returns:
        list: Navigation items with URLs and metadata
    """
    items = []
    
    try:
        items = [
            {
                'name': 'Dashboard',
                'url': reverse('gso_dashboard_main'),
                'icon': 'dashboard',
                'section': 'main'
            },
            {
                'name': 'Inventory',
                'url': reverse('gso_inventory'),
                'icon': 'inventory',
                'section': 'inventory',
                'submenu': [
                    {
                        'name': 'Add Item',
                        'url': reverse('gso_inventory_add'),
                        'icon': 'add'
                    },
                    {
                        'name': 'Stock Adjustment',
                        'url': reverse('gso_stock_adjustment'),
                        'icon': 'adjustment'
                    },
                    {
                        'name': 'Low Stock Alerts',
                        'url': reverse('gso_low_stock'),
                        'icon': 'warning'
                    },
                    {
                        'name': 'Transactions',
                        'url': reverse('gso_inventory_transactions'),
                        'icon': 'transactions'
                    }
                ]
            },
            {
                'name': 'Approvals',
                'url': reverse('gso_approvals'),
                'icon': 'approvals',
                'section': 'approvals',
                'submenu': [
                    {
                        'name': 'Pending Requests',
                        'url': reverse('gso_approvals_pending'),
                        'icon': 'pending'
                    },
                    {
                        'name': 'History',
                        'url': reverse('gso_approval_history'),
                        'icon': 'history'
                    }
                ]
            },
            {
                'name': 'Requests',
                'url': reverse('gso_requests'),
                'icon': 'requests',
                'section': 'requests'
            },
            {
                'name': 'QR Scanner',
                'url': reverse('gso_qr_scanner'),
                'icon': 'qr',
                'section': 'qr',
                'submenu': [
                    {
                        'name': 'Scan Tool',
                        'url': reverse('gso_qr_scanner_tool'),
                        'icon': 'scan'
                    },
                    {
                        'name': 'Scan History',
                        'url': reverse('gso_scan_history'),
                        'icon': 'history'
                    },
                    {
                        'name': 'QR Management',
                        'url': reverse('gso_qr_management'),
                        'icon': 'management'
                    },
                    {
                        'name': 'QR Code List',
                        'url': reverse('gso_qr_list'),
                        'icon': 'list'
                    }
                ]
            },
            {
                'name': 'Reports',
                'url': reverse('gso_reports'),
                'icon': 'reports',
                'section': 'reports',
                'submenu': [
                    {
                        'name': 'Inventory Report',
                        'url': reverse('gso_inventory_report'),
                        'icon': 'inventory'
                    },
                    {
                        'name': 'Request Report',
                        'url': reverse('gso_request_report'),
                        'icon': 'requests'
                    },
                    {
                        'name': 'Usage Report',
                        'url': reverse('gso_usage_report'),
                        'icon': 'usage'
                    },
                    {
                        'name': 'Analytics',
                        'url': reverse('gso_analytics'),
                        'icon': 'analytics'
                    }
                ]
            }
        ]
    except NoReverseMatch as e:
        logger.warning(f"Error generating GSO navigation: {e}")
    
    return items


def get_admin_navigation_items():
    """
    Returns navigation items for admin users.

    Returns:
        list: Navigation items with URLs and metadata
    """
    items = []

    try:
        items = [
            {
                'name': 'Dashboard',
                'url': reverse('admin_dashboard'),
                'icon': 'dashboard',
                'section': 'main'
            },
            {
                'name': 'User Management',
                'url': reverse('admin_users'),
                'icon': 'users',
                'section': 'users',
                'submenu': [
                    {
                        'name': 'View All Users',
                        'url': reverse('admin_users_list'),
                        'icon': 'list'
                    },
                    {
                        'name': 'Add User',
                        'url': reverse('admin_users_add'),
                        'icon': 'add'
                    },
                    {
                        'name': 'Manage Roles',
                        'url': reverse('admin_roles_management'),
                        'icon': 'roles'
                    },
                    {
                        'name': 'User Permissions',
                        'url': reverse('admin_permissions'),
                        'icon': 'permissions'
                    }
                ]
            },
            {
                'name': 'System Settings',
                'url': reverse('admin_settings'),
                'icon': 'settings',
                'section': 'settings',
                'submenu': [
                    {
                        'name': 'General Settings',
                        'url': reverse('admin_settings_general'),
                        'icon': 'general'
                    },
                    {
                        'name': 'Email Configuration',
                        'url': reverse('admin_settings_email'),
                        'icon': 'email'
                    },
                    {
                        'name': 'Backup Settings',
                        'url': reverse('admin_settings_backup'),
                        'icon': 'backup'
                    },
                    {
                        'name': 'Security Settings',
                        'url': reverse('admin_settings_security'),
                        'icon': 'security'
                    }
                ]
            },
            {
                'name': 'Inventory Management',
                'url': reverse('admin_inventory'),
                'icon': 'inventory',
                'section': 'inventory'
            },
            {
                'name': 'Request Management',
                'url': reverse('admin_requests'),
                'icon': 'requests',
                'section': 'requests',
                'submenu': [
                    {
                        'name': 'All Requests',
                        'url': reverse('request_list'),
                        'icon': 'list'
                    },
                    {
                        'name': 'Pending Approvals',
                        'url': reverse('pending_requests_view'),
                        'icon': 'pending'
                    },
                    {
                        'name': 'Approval History',
                        'url': reverse('approval_history_view'),
                        'icon': 'history'
                    },
                    {
                        'name': 'Bulk Operations',
                        'url': reverse('admin_bulk_operations'),
                        'icon': 'bulk'
                    }
                ]
            },
            {
                'name': 'Reports & Analytics',
                'url': reverse('reports_dashboard'),
                'icon': 'reports',
                'section': 'reports',
                'submenu': [
                    {
                        'name': 'Inventory Reports',
                        'url': reverse('inventory_report'),
                        'icon': 'inventory'
                    },
                    {
                        'name': 'Request Reports',
                        'url': reverse('request_summary_report'),
                        'icon': 'requests'
                    },
                    {
                        'name': 'Usage Reports',
                        'url': reverse('usage_log_report'),
                        'icon': 'usage'
                    },
                    {
                        'name': 'Analytics Dashboard',
                        'url': reverse('analytics_dashboard'),
                        'icon': 'analytics'
                    },
                    {
                        'name': 'System Performance',
                        'url': reverse('admin_performance_reports'),
                        'icon': 'performance'
                    }
                ]
            },
            {
                'name': 'Audit Logs',
                'url': reverse('admin_audit_logs'),
                'icon': 'audit',
                'section': 'audit'
            },
            {
                'name': 'Notifications',
                'url': reverse('admin_notifications'),
                'icon': 'notifications',
                'section': 'notifications',
                'submenu': [
                    {
                        'name': 'All Notifications',
                        'url': reverse('admin_notifications_list'),
                        'icon': 'list'
                    },
                    {
                        'name': 'Create Notification',
                        'url': reverse('create_system_notification'),
                        'icon': 'create'
                    },
                    {
                        'name': 'Templates',
                        'url': reverse('admin_notification_templates'),
                        'icon': 'templates'
                    },
                    {
                        'name': 'Settings',
                        'url': reverse('admin_notification_settings'),
                        'icon': 'settings'
                    }
                ]
            },
            {
                'name': 'System Health',
                'url': reverse('admin_system_health'),
                'icon': 'health',
                'section': 'health'
            }
        ]
    except NoReverseMatch as e:
        logger.warning(f"Error generating admin navigation: {e}")

    return items


def get_user_navigation_items():
    """
    Returns navigation items for regular users.
    
    Returns:
        list: Navigation items with URLs and metadata
    """
    # Placeholder for user navigation
    return []


def generate_gso_breadcrumbs(path):
    """
    Generates breadcrumbs for GSO paths.
    
    Args:
        path: Current request path
    
    Returns:
        list: Breadcrumb items
    """
    breadcrumbs = []
    
    try:
        breadcrumbs.append({
            'name': 'GSO',
            'url': reverse('gso_dashboard_main')
        })
        
        # Parse path segments
        segments = path.strip('/').split('/')
        
        if len(segments) > 1 and segments[0] == 'gso':
            section = segments[1] if len(segments) > 1 else None
            
            if section == 'dashboard':
                breadcrumbs.append({'name': 'Dashboard', 'url': None})
            elif section == 'inventory':
                breadcrumbs.append({'name': 'Inventory', 'url': reverse('gso_inventory')})
                if len(segments) > 2:
                    subsection = segments[2]
                    if subsection == 'add':
                        breadcrumbs.append({'name': 'Add Item', 'url': None})
                    elif subsection == 'low-stock':
                        breadcrumbs.append({'name': 'Low Stock Alerts', 'url': None})
                    elif subsection == 'transactions':
                        breadcrumbs.append({'name': 'Transactions', 'url': None})
                    elif subsection == 'stock-adjustment':
                        breadcrumbs.append({'name': 'Stock Adjustment', 'url': None})
            elif section == 'approvals':
                breadcrumbs.append({'name': 'Approvals', 'url': reverse('gso_approvals')})
                if len(segments) > 2:
                    subsection = segments[2]
                    if subsection == 'pending':
                        breadcrumbs.append({'name': 'Pending Requests', 'url': None})
                    elif subsection == 'history':
                        breadcrumbs.append({'name': 'History', 'url': None})
            elif section == 'requests':
                breadcrumbs.append({'name': 'Requests', 'url': None})
            elif section == 'qr-scanner':
                breadcrumbs.append({'name': 'QR Scanner', 'url': reverse('gso_qr_scanner')})
                if len(segments) > 2:
                    subsection = segments[2]
                    if subsection == 'scan':
                        breadcrumbs.append({'name': 'Scan Tool', 'url': None})
                    elif subsection == 'history':
                        breadcrumbs.append({'name': 'Scan History', 'url': None})
            elif section == 'qr-codes':
                breadcrumbs.append({'name': 'QR Scanner', 'url': reverse('gso_qr_scanner')})
                if len(segments) > 2:
                    subsection = segments[2]
                    if subsection == 'list':
                        breadcrumbs.append({'name': 'QR Code List', 'url': None})
                    else:
                        breadcrumbs.append({'name': 'QR Management', 'url': None})
            elif section == 'reports':
                breadcrumbs.append({'name': 'Reports', 'url': reverse('gso_reports')})
                if len(segments) > 2:
                    subsection = segments[2]
                    if subsection == 'inventory':
                        breadcrumbs.append({'name': 'Inventory Report', 'url': None})
                    elif subsection == 'requests':
                        breadcrumbs.append({'name': 'Request Report', 'url': None})
                    elif subsection == 'usage':
                        breadcrumbs.append({'name': 'Usage Report', 'url': None})
                    elif subsection == 'analytics':
                        breadcrumbs.append({'name': 'Analytics', 'url': None})
    
    except NoReverseMatch as e:
        logger.warning(f"Error generating GSO breadcrumbs: {e}")
    
    return breadcrumbs


def generate_admin_breadcrumbs(path):
    """
    Generates breadcrumbs for admin paths.

    Args:
        path: Current request path

    Returns:
        list: Breadcrumb items
    """
    breadcrumbs = []

    try:
        breadcrumbs.append({
            'name': 'Admin',
            'url': reverse('admin_dashboard')
        })

        # Parse path segments
        segments = path.strip('/').split('/')

        if len(segments) > 1:
            if segments[0] == 'admin-dashboard':
                breadcrumbs.append({'name': 'Dashboard', 'url': None})
            elif segments[0] == 'admin-panel' and len(segments) > 1:
                section = segments[1]

                if section == 'users':
                    breadcrumbs.append({'name': 'User Management', 'url': reverse('admin_users')})
                    if len(segments) > 2:
                        subsection = segments[2]
                        if subsection == 'list':
                            breadcrumbs.append({'name': 'User List', 'url': None})
                        elif subsection == 'add':
                            breadcrumbs.append({'name': 'Add User', 'url': None})
                        elif subsection == 'roles':
                            breadcrumbs.append({'name': 'Manage Roles', 'url': None})
                        elif subsection == 'permissions':
                            breadcrumbs.append({'name': 'User Permissions', 'url': None})

                elif section == 'settings':
                    breadcrumbs.append({'name': 'System Settings', 'url': reverse('admin_settings')})
                    if len(segments) > 2:
                        subsection = segments[2]
                        if subsection == 'general':
                            breadcrumbs.append({'name': 'General Settings', 'url': None})
                        elif subsection == 'email':
                            breadcrumbs.append({'name': 'Email Configuration', 'url': None})
                        elif subsection == 'backup':
                            breadcrumbs.append({'name': 'Backup Settings', 'url': None})
                        elif subsection == 'security':
                            breadcrumbs.append({'name': 'Security Settings', 'url': None})

                elif section == 'inventory':
                    breadcrumbs.append({'name': 'Inventory Management', 'url': None})

                elif section == 'requests':
                    breadcrumbs.append({'name': 'Request Management', 'url': reverse('admin_requests')})
                    if len(segments) > 2:
                        subsection = segments[2]
                        if subsection == 'bulk-operations':
                            breadcrumbs.append({'name': 'Bulk Operations', 'url': None})

                elif section == 'reports':
                    breadcrumbs.append({'name': 'Reports & Analytics', 'url': reverse('reports_dashboard')})
                    if len(segments) > 2:
                        subsection = segments[2]
                        if subsection == 'performance':
                            breadcrumbs.append({'name': 'System Performance', 'url': None})

                elif section == 'audit-logs':
                    breadcrumbs.append({'name': 'Audit Logs', 'url': None})

                elif section == 'notifications':
                    breadcrumbs.append({'name': 'Notifications', 'url': reverse('admin_notifications')})
                    if len(segments) > 2:
                        subsection = segments[2]
                        if subsection == 'list':
                            breadcrumbs.append({'name': 'All Notifications', 'url': None})
                        elif subsection == 'create':
                            breadcrumbs.append({'name': 'Create Notification', 'url': None})
                        elif subsection == 'templates':
                            breadcrumbs.append({'name': 'Templates', 'url': None})
                        elif subsection == 'settings':
                            breadcrumbs.append({'name': 'Settings', 'url': None})

                elif section == 'system-health':
                    breadcrumbs.append({'name': 'System Health', 'url': None})

    except NoReverseMatch as e:
        logger.warning(f"Error generating admin breadcrumbs: {e}")

    return breadcrumbs


def generate_user_breadcrumbs(path):
    """
    Generates breadcrumbs for user paths.
    
    Args:
        path: Current request path
    
    Returns:
        list: Breadcrumb items
    """
    # Placeholder for user breadcrumbs
    return []
