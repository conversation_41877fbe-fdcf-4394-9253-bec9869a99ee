#!/usr/bin/env python3
"""
Test script to verify admin HTMX endpoints are working correctly.
"""

import os
import sys
import requests
from urllib.parse import urljoin

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SmartSupply.settings')

import django
django.setup()

from django.urls import reverse


def test_admin_htmx_endpoints():
    """Test all admin HTMX endpoints."""
    print("🔧 Testing Admin HTMX Endpoints")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # List of admin HTMX endpoints to test
    htmx_endpoints = [
        'admin_users_count',
        'admin_inventory_alerts_count',
        'admin_pending_requests_count',
        'admin_audit_alerts_count',
        'admin_notification_alerts_count',
        'admin_system_health_indicator',
        'admin_quick_stats_htmx'
    ]
    
    working_endpoints = []
    failing_endpoints = []
    
    for endpoint_name in htmx_endpoints:
        try:
            # Get URL from Django
            url_path = reverse(endpoint_name)
            full_url = urljoin(base_url, url_path)
            
            # Make request
            response = requests.get(full_url, timeout=10)
            
            if response.status_code == 200:
                # Check if response contains error message
                content = response.text.lower()
                if 'error' in content and 'models' in content:
                    failing_endpoints.append((endpoint_name, f"Contains models error: {response.text[:100]}"))
                    print(f"❌ {endpoint_name}: Contains models error")
                elif 'error' in content:
                    failing_endpoints.append((endpoint_name, f"Contains error: {response.text[:100]}"))
                    print(f"⚠️  {endpoint_name}: Contains error - {response.text[:50]}...")
                else:
                    working_endpoints.append((endpoint_name, response.text[:50]))
                    print(f"✅ {endpoint_name}: Working - {response.text[:50]}...")
            else:
                failing_endpoints.append((endpoint_name, f"HTTP {response.status_code}"))
                print(f"❌ {endpoint_name}: HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            failing_endpoints.append((endpoint_name, f"Request error: {e}"))
            print(f"❌ {endpoint_name}: Request error - {e}")
        except Exception as e:
            failing_endpoints.append((endpoint_name, f"Error: {e}"))
            print(f"❌ {endpoint_name}: Error - {e}")
    
    # Summary
    print(f"\n📊 Results:")
    print(f"✅ Working endpoints: {len(working_endpoints)}")
    print(f"❌ Failing endpoints: {len(failing_endpoints)}")
    
    if failing_endpoints:
        print(f"\n❌ Failed endpoints:")
        for endpoint, error in failing_endpoints:
            print(f"   • {endpoint}: {error}")
    
    return len(working_endpoints), len(failing_endpoints)


def test_admin_dashboard_access():
    """Test admin dashboard access."""
    print("\n🏠 Testing Admin Dashboard Access")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    test_urls = [
        "/admin-dashboard/",
        "/admin-panel/users/",
        "/admin-panel/settings/",
        "/admin-panel/notifications/"
    ]
    
    accessible = []
    inaccessible = []
    
    for url_path in test_urls:
        try:
            full_url = urljoin(base_url, url_path)
            response = requests.get(full_url, timeout=10, allow_redirects=True)
            
            if response.status_code in [200, 302]:
                accessible.append((url_path, response.status_code))
                print(f"✅ {url_path}: HTTP {response.status_code}")
            else:
                inaccessible.append((url_path, response.status_code))
                print(f"❌ {url_path}: HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            inaccessible.append((url_path, f"Request error: {e}"))
            print(f"❌ {url_path}: Request error - {e}")
    
    print(f"\n📊 Dashboard Access Results:")
    print(f"✅ Accessible pages: {len(accessible)}")
    print(f"❌ Inaccessible pages: {len(inaccessible)}")
    
    return len(accessible), len(inaccessible)


def test_models_import_fix():
    """Test that the models import fix is working."""
    print("\n🔧 Testing Models Import Fix")
    print("=" * 50)
    
    try:
        # Test the specific function that had the issue
        from suptrack.admin_views import admin_quick_stats_htmx, admin_inventory_alerts_count
        from django.test import RequestFactory
        from django.contrib.auth.models import User
        from suptrack.models import UserProfile
        
        # Create a mock request
        factory = RequestFactory()
        request = factory.get('/test/')
        
        # Create a test admin user
        try:
            admin_user = User.objects.get(username='admin')
        except User.DoesNotExist:
            admin_user = User.objects.create_user(username='admin_test', password='test')
            UserProfile.objects.get_or_create(user=admin_user, defaults={'role': 'admin'})
        
        request.user = admin_user
        
        # Test admin_quick_stats_htmx
        try:
            response = admin_quick_stats_htmx(request)
            content = response.content.decode('utf-8')
            if 'models' in content.lower() and 'error' in content.lower():
                print("❌ admin_quick_stats_htmx: Still has models error")
                return False
            else:
                print("✅ admin_quick_stats_htmx: Working correctly")
        except Exception as e:
            print(f"❌ admin_quick_stats_htmx: Error - {e}")
            return False
        
        # Test admin_inventory_alerts_count
        try:
            response = admin_inventory_alerts_count(request)
            content = response.content.decode('utf-8')
            if 'models' in content.lower() and 'error' in content.lower():
                print("❌ admin_inventory_alerts_count: Still has models error")
                return False
            else:
                print("✅ admin_inventory_alerts_count: Working correctly")
        except Exception as e:
            print(f"❌ admin_inventory_alerts_count: Error - {e}")
            return False
        
        print("✅ Models import fix is working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Models import fix test failed: {e}")
        return False


def main():
    """Run all admin tests."""
    print("🚀 Admin HTMX Endpoints Test Suite")
    print("=" * 70)
    
    results = []
    
    # Test HTMX endpoints
    working_htmx, failing_htmx = test_admin_htmx_endpoints()
    results.append(('HTMX Endpoints', working_htmx > failing_htmx and failing_htmx == 0))
    
    # Test dashboard access
    accessible_pages, inaccessible_pages = test_admin_dashboard_access()
    results.append(('Dashboard Access', accessible_pages > inaccessible_pages))
    
    # Test models import fix
    models_fix_working = test_models_import_fix()
    results.append(('Models Import Fix', models_fix_working))
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 COMPREHENSIVE TEST SUMMARY")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All admin HTMX endpoints are working correctly!")
        print("\n✅ Key Fixes Applied:")
        print("   • Fixed 'models' import error in admin_views.py")
        print("   • Added proper F import from django.db.models")
        print("   • All HTMX endpoints now working")
        print("   • Admin dashboard fully functional")
        return True
    else:
        print("⚠️  Some issues remain. Check the output above for details.")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
