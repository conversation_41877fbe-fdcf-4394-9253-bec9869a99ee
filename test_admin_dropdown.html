<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dropdown Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Admin Navigation CSS */
        .nav-submenu {
            transition: all 0.3s ease-in-out;
            overflow: hidden;
            transform-origin: top;
        }

        .nav-submenu.hidden {
            max-height: 0;
            opacity: 0;
            transform: scaleY(0);
            margin-top: 0;
            margin-bottom: 0;
        }

        .nav-submenu:not(.hidden) {
            max-height: 500px;
            opacity: 1;
            transform: scaleY(1);
            margin-top: 0.25rem;
            margin-bottom: 0.25rem;
        }

        .nav-submenu .nav-sublink {
            margin-bottom: 0.125rem;
        }

        .nav-submenu .nav-sublink:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-xl font-bold mb-6">Admin Dropdown Test</h1>
        
        <!-- Test Navigation Section -->
        <div class="space-y-2">
            <!-- User Management -->
            <div class="nav-section" data-nav-section="users">
                <a href="#" 
                   class="nav-link flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                   data-nav-item="users">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    User Management
                    <div class="ml-auto bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">5</div>
                    <svg class="w-4 h-4 ml-2 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </a>
                
                <!-- User Management submenu -->
                <div class="nav-submenu ml-8 mt-1 space-y-1 hidden">
                    <a href="#" class="nav-sublink flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                        </svg>
                        View All Users
                    </a>
                    <a href="#" class="nav-sublink flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add User
                    </a>
                    <a href="#" class="nav-sublink flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                        Manage Roles
                    </a>
                </div>
            </div>

            <!-- System Settings -->
            <div class="nav-section" data-nav-section="settings">
                <a href="#" 
                   class="nav-link flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                   data-nav-item="settings">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    System Settings
                    <svg class="w-4 h-4 ml-auto transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </a>
                
                <!-- System Settings submenu -->
                <div class="nav-submenu ml-8 mt-1 space-y-1 hidden">
                    <a href="#" class="nav-sublink flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                        </svg>
                        General Settings
                    </a>
                    <a href="#" class="nav-sublink flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Email Configuration
                    </a>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div id="test-results" class="mt-6"></div>
        
        <!-- Test Button -->
        <button id="test-dropdown" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
            Test Dropdown Functionality
        </button>
    </div>

    <script>
        // Admin Navigation Dropdown Test
        class AdminDropdownTest {
            constructor() {
                this.setupEventListeners();
            }

            setupEventListeners() {
                // Handle navigation link clicks (same as admin navigation)
                document.addEventListener('click', (e) => {
                    const navSection = e.target.closest('.nav-section');
                    if (navSection && e.target.closest('.nav-link')) {
                        const submenu = navSection.querySelector('.nav-submenu');
                        if (submenu) {
                            e.preventDefault();
                            this.toggleSubmenu(navSection);
                        }
                    }
                });

                // Test button
                document.getElementById('test-dropdown').addEventListener('click', () => {
                    this.runDropdownTest();
                });
            }

            toggleSubmenu(navSection) {
                const submenu = navSection.querySelector('.nav-submenu');
                const chevron = navSection.querySelector('.nav-link svg:last-child');
                const sectionName = navSection.getAttribute('data-nav-section');
                
                if (!submenu) return;
                
                const isHidden = submenu.classList.contains('hidden');
                
                if (isHidden) {
                    // Show submenu
                    submenu.classList.remove('hidden');
                    chevron?.classList.add('rotate-180');
                    console.log(`Opened ${sectionName} submenu`);
                } else {
                    // Hide submenu
                    submenu.classList.add('hidden');
                    chevron?.classList.remove('rotate-180');
                    console.log(`Closed ${sectionName} submenu`);
                }
            }

            runDropdownTest() {
                const results = [];
                
                // Test 1: Check if nav sections exist
                const navSections = document.querySelectorAll('.nav-section');
                results.push({
                    test: 'Navigation Sections',
                    result: navSections.length > 0,
                    details: `Found ${navSections.length} navigation sections`
                });

                // Test 2: Check if submenus exist
                const submenus = document.querySelectorAll('.nav-submenu');
                results.push({
                    test: 'Submenus',
                    result: submenus.length > 0,
                    details: `Found ${submenus.length} submenus`
                });

                // Test 3: Check if chevrons exist
                const chevrons = document.querySelectorAll('.nav-link svg:last-child');
                results.push({
                    test: 'Chevron Icons',
                    result: chevrons.length > 0,
                    details: `Found ${chevrons.length} chevron icons`
                });

                // Test 4: Test dropdown functionality
                const firstSection = navSections[0];
                if (firstSection) {
                    const submenu = firstSection.querySelector('.nav-submenu');
                    const initiallyHidden = submenu?.classList.contains('hidden');
                    
                    // Simulate click
                    const navLink = firstSection.querySelector('.nav-link');
                    navLink?.click();
                    
                    const nowVisible = !submenu?.classList.contains('hidden');
                    results.push({
                        test: 'Dropdown Toggle',
                        result: initiallyHidden && nowVisible,
                        details: `Initially hidden: ${initiallyHidden}, Now visible: ${nowVisible}`
                    });
                }

                this.displayResults(results);
            }

            displayResults(results) {
                const container = document.getElementById('test-results');
                const passed = results.filter(r => r.result).length;
                const total = results.length;
                
                let html = `<h3 class="font-bold mb-2">Test Results: ${passed}/${total} Passed</h3>`;
                
                results.forEach(result => {
                    const icon = result.result ? '✅' : '❌';
                    const bgColor = result.result ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                    
                    html += `
                        <div class="mb-2 p-2 rounded ${bgColor}">
                            ${icon} <strong>${result.test}:</strong> ${result.details}
                        </div>
                    `;
                });
                
                container.innerHTML = html;
            }
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            window.adminDropdownTest = new AdminDropdownTest();
            console.log('Admin Dropdown Test initialized');
        });
    </script>
</body>
</html>
