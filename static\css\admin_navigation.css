/* Admin Navigation Styles */

/* Navigation Links */
.nav-link {
    position: relative;
    transition: all 0.2s ease-in-out;
    border-radius: 0.5rem;
}

.nav-link:hover {
    background-color: #f3f4f6;
    transform: translateX(2px);
}

.nav-link.active {
    background-color: #dbeafe !important;
    color: #1d4ed8 !important;
    border-right: 3px solid #3b82f6;
    font-weight: 600;
}

.nav-link.active svg {
    color: #1d4ed8;
}

/* Sublinks */
.nav-sublink {
    position: relative;
    transition: all 0.2s ease-in-out;
    border-radius: 0.5rem;
    margin-left: 1rem;
}

.nav-sublink:hover {
    background-color: #f9fafb;
    transform: translateX(2px);
}

.nav-sublink.active {
    background-color: #eff6ff !important;
    color: #1e40af !important;
    border-left: 2px solid #3b82f6;
    font-weight: 500;
}

.nav-sublink.active svg {
    color: #1e40af;
}

/* Submenu Animation */
.nav-submenu {
    transition: all 0.3s ease-in-out;
    overflow: hidden;
    transform-origin: top;
}

.nav-submenu.hidden {
    max-height: 0;
    opacity: 0;
    transform: scaleY(0);
    margin-top: 0;
    margin-bottom: 0;
}

.nav-submenu:not(.hidden) {
    max-height: 500px;
    opacity: 1;
    transform: scaleY(1);
    margin-top: 0.25rem;
    margin-bottom: 0.25rem;
}

/* Ensure submenu items are properly spaced */
.nav-submenu .nav-sublink {
    margin-bottom: 0.125rem;
}

.nav-submenu .nav-sublink:last-child {
    margin-bottom: 0;
}

/* Chevron Animation */
.nav-link svg:last-child {
    transition: transform 0.2s ease-in-out;
}

.nav-link svg:last-child.rotate-180 {
    transform: rotate(180deg);
}

/* Badge Styles */
.nav-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 1.25rem;
    height: 1.25rem;
    padding: 0 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
    color: white;
    background-color: #ef4444;
    border-radius: 9999px;
    animation: pulse 2s infinite;
}

.nav-badge.success {
    background-color: #10b981;
}

.nav-badge.warning {
    background-color: #f59e0b;
}

.nav-badge.info {
    background-color: #3b82f6;
}

/* System Health Indicator */
.health-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.health-indicator.healthy {
    background-color: #10b981;
}

.health-indicator.warning {
    background-color: #f59e0b;
}

.health-indicator.critical {
    background-color: #ef4444;
}

.health-indicator.unknown {
    background-color: #6b7280;
}

/* Admin Panel Header */
.admin-panel-header {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: white;
}

/* Quick Stats */
.admin-quick-stats {
    font-size: 0.75rem;
    line-height: 1.25rem;
}

.admin-quick-stats .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.admin-quick-stats .stat-item:last-child {
    border-bottom: none;
}

.admin-quick-stats .stat-value {
    font-weight: 600;
    color: #374151;
}

/* Keyboard Shortcut Indicators */
.nav-link[title]:hover::after {
    content: attr(title);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    margin-left: 0.5rem;
    padding: 0.25rem 0.5rem;
    background-color: #1f2937;
    color: white;
    font-size: 0.75rem;
    border-radius: 0.25rem;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
}

/* Loading States */
.nav-link.loading {
    opacity: 0.6;
    pointer-events: none;
}

.nav-link.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1rem;
    height: 1rem;
    margin: -0.5rem 0 0 -0.5rem;
    border: 2px solid #e5e7eb;
    border-top-color: #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Animations */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Section Dividers */
.nav-section + .nav-section {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

/* Focus States for Accessibility */
.nav-link:focus,
.nav-sublink:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .nav-link {
        padding: 0.75rem 1rem;
    }
    
    .nav-sublink {
        padding: 0.5rem 1rem;
        margin-left: 0.5rem;
    }
    
    .nav-link[title]:hover::after {
        display: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .nav-link:hover {
        background-color: #374151;
    }
    
    .nav-link.active {
        background-color: #1e3a8a !important;
        color: #93c5fd !important;
    }
    
    .nav-sublink:hover {
        background-color: #4b5563;
    }
    
    .nav-sublink.active {
        background-color: #1e40af !important;
        color: #bfdbfe !important;
    }
}

/* Print Styles */
@media print {
    .nav-link,
    .nav-sublink,
    .nav-submenu {
        display: none !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .nav-link.active {
        border-right-width: 4px;
        border-right-color: #000;
    }
    
    .nav-sublink.active {
        border-left-width: 3px;
        border-left-color: #000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .nav-link,
    .nav-sublink,
    .nav-submenu,
    .nav-link svg:last-child {
        transition: none;
    }
    
    .nav-badge,
    .health-indicator {
        animation: none;
    }
}
