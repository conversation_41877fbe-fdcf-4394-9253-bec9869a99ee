"""
UI tests for GSO navigation functionality.
Tests the JavaScript behavior and submenu toggles.
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.test.utils import override_settings

from suptrack.models import UserProfile


class GSONavigationUITestCase(TestCase):
    """Test GSO navigation UI functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gso_staff',
            email='<EMAIL>',
            password='testpass123',
            first_name='GSO',
            last_name='Staff'
        )
        
        self.gso_profile = UserProfile.objects.create(
            user=self.gso_user,
            role='gso_staff',
            department='Government Services Office'
        )

    def test_gso_navigation_javascript_included(self):
        """Test that GSO navigation JavaScript is properly included"""
        self.client.login(username='gso_staff', password='testpass123')

        response = self.client.get(reverse('gso_dashboard_main'))

        # Should include the GSO navigation JavaScript
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'gso_navigation.js')

    def test_debug_navigation_disabled(self):
        """Test that debug navigation button is disabled"""
        self.client.login(username='gso_staff', password='testpass123')

        response = self.client.get(reverse('gso_dashboard_main'))

        # Should not contain debug button text
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, 'Debug Nav')

    def test_submenu_structure_present(self):
        """Test that submenu structure is properly rendered"""
        self.client.login(username='gso_staff', password='testpass123')
        
        response = self.client.get(reverse('gso_dashboard_main'))
        
        # Should contain proper navigation structure
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'nav-section')
        self.assertContains(response, 'nav-submenu')
        self.assertContains(response, 'data-nav-section')
        self.assertContains(response, 'data-nav-item')

    def test_navigation_data_attributes(self):
        """Test that navigation elements have proper data attributes"""
        self.client.login(username='gso_staff', password='testpass123')
        
        response = self.client.get(reverse('gso_dashboard_main'))
        
        # Should contain navigation data attributes
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'data-nav-section="inventory"')
        self.assertContains(response, 'data-nav-section="approvals"')
        self.assertContains(response, 'data-nav-section="qr"')
        self.assertContains(response, 'data-nav-section="reports"')

    def test_submenu_toggle_arrows_present(self):
        """Test that submenu toggle arrows are present"""
        self.client.login(username='gso_staff', password='testpass123')
        
        response = self.client.get(reverse('gso_dashboard_main'))
        
        # Should contain arrow SVGs for submenu toggles
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'transform transition-transform')
        self.assertContains(response, 'M19 9l-7 7-7-7')  # Down arrow path

    def test_navigation_css_classes(self):
        """Test that navigation CSS classes are present"""
        self.client.login(username='gso_staff', password='testpass123')
        
        response = self.client.get(reverse('gso_dashboard_main'))
        
        # Should contain navigation CSS classes
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'nav-link')
        self.assertContains(response, 'nav-sublink')
        self.assertContains(response, 'nav-submenu')
        self.assertContains(response, 'hidden')  # For collapsed submenus

    def test_navigation_accessibility(self):
        """Test that navigation has proper accessibility features"""
        self.client.login(username='gso_staff', password='testpass123')
        
        response = self.client.get(reverse('gso_dashboard_main'))
        
        # Should contain accessibility features
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'transition-colors')
        self.assertContains(response, 'hover:bg-gray-100')
        self.assertContains(response, 'rounded-lg')

    def test_keyboard_shortcuts_javascript(self):
        """Test that keyboard shortcuts are properly configured"""
        self.client.login(username='gso_staff', password='testpass123')

        response = self.client.get(reverse('gso_dashboard_main'))

        # Should include the GSO navigation JavaScript file
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'gso_navigation.js')

    def test_navigation_state_persistence(self):
        """Test that navigation state persistence is configured"""
        self.client.login(username='gso_staff', password='testpass123')

        response = self.client.get(reverse('gso_dashboard_main'))

        # Should include the GSO navigation JavaScript file which handles persistence
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'gso_navigation.js')


class GSONavigationFunctionalTestCase(TestCase):
    """Functional tests for GSO navigation behavior"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gso_staff',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.gso_profile = UserProfile.objects.create(
            user=self.gso_user,
            role='gso_staff',
            department='GSO'
        )

    def test_navigation_initialization(self):
        """Test that navigation initializes properly"""
        self.client.login(username='gso_staff', password='testpass123')

        response = self.client.get(reverse('gso_dashboard_main'))

        # Should initialize navigation
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'GSONavigation')

    def test_active_navigation_highlighting(self):
        """Test that active navigation items are properly highlighted"""
        self.client.login(username='gso_staff', password='testpass123')
        
        # Test different pages
        pages = [
            ('gso_dashboard_main', 'dashboard'),
            ('gso_inventory', 'inventory'),
            ('gso_approvals', 'approvals'),
        ]
        
        for url_name, expected_active in pages:
            response = self.client.get(reverse(url_name))
            self.assertEqual(response.status_code, 200)
            # The JavaScript should handle active state highlighting
            self.assertContains(response, 'nav-link')

    def test_submenu_state_management(self):
        """Test that submenu states are properly managed"""
        self.client.login(username='gso_staff', password='testpass123')

        response = self.client.get(reverse('gso_dashboard_main'))

        # Should include the GSO navigation JavaScript file which handles state management
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'gso_navigation.js')
