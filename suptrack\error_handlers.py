"""
Error handling utilities for the Smart Supply Management System.
Provides consistent error handling across all views and API endpoints.
"""

from django.shortcuts import render, redirect
from django.http import Json<PERSON>esponse, Http404
from django.contrib import messages
from django.core.exceptions import PermissionDenied, ValidationError
from django.urls import reverse
from django.utils import timezone
from functools import wraps
import logging

logger = logging.getLogger(__name__)


class SmartSupplyError(Exception):
    """Base exception class for Smart Supply application errors."""
    
    def __init__(self, message, error_code=None, status_code=400):
        self.message = message
        self.error_code = error_code or 'GENERIC_ERROR'
        self.status_code = status_code
        super().__init__(self.message)


class InventoryError(SmartSupplyError):
    """Exception for inventory-related errors."""
    
    def __init__(self, message, error_code=None):
        super().__init__(message, error_code or 'INVENTORY_ERROR', 400)


class RequestError(SmartSupplyError):
    """Exception for request-related errors."""
    
    def __init__(self, message, error_code=None):
        super().__init__(message, error_code or 'REQUEST_ERROR', 400)


class QRCodeError(SmartSupplyError):
    """Exception for QR code-related errors."""
    
    def __init__(self, message, error_code=None):
        super().__init__(message, error_code or 'QR_CODE_ERROR', 400)


def handle_view_errors(view_func):
    """
    Decorator to handle common view errors consistently.
    
    Args:
        view_func: The view function to wrap
    
    Returns:
        Wrapped view function with error handling
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        try:
            return view_func(request, *args, **kwargs)
        
        except PermissionDenied as e:
            logger.warning(f"Permission denied in {view_func.__name__}: {e}")
            return handle_permission_denied(request, str(e))
        
        except Http404 as e:
            logger.warning(f"404 error in {view_func.__name__}: {e}")
            return handle_not_found(request, str(e))
        
        except ValidationError as e:
            logger.warning(f"Validation error in {view_func.__name__}: {e}")
            return handle_validation_error(request, e)
        
        except SmartSupplyError as e:
            logger.warning(f"Smart Supply error in {view_func.__name__}: {e}")
            return handle_smart_supply_error(request, e)
        
        except Exception as e:
            logger.error(f"Unexpected error in {view_func.__name__}: {e}", exc_info=True)
            return handle_server_error(request, str(e))
    
    return wrapper


def handle_permission_denied(request, message="Access denied"):
    """
    Handle permission denied errors.
    
    Args:
        request: Django request object
        message: Error message
    
    Returns:
        Appropriate response for permission denied
    """
    if request.headers.get('HX-Request'):
        return JsonResponse({
            'success': False,
            'message': message,
            'error_code': 'PERMISSION_DENIED'
        }, status=403)
    
    messages.error(request, message)
    
    # Redirect to appropriate dashboard based on user role
    if request.user.is_authenticated:
        try:
            user_role = request.user.userprofile.role
            if user_role == 'gso_staff':
                return redirect('gso_dashboard_main')
            elif user_role == 'admin':
                return redirect('admin_dashboard')
            else:
                return redirect('dashboard')
        except:
            return redirect('dashboard')
    else:
        return redirect('login')


def handle_not_found(request, message="Resource not found"):
    """
    Handle 404 not found errors.
    
    Args:
        request: Django request object
        message: Error message
    
    Returns:
        Appropriate response for not found
    """
    if request.headers.get('HX-Request'):
        return JsonResponse({
            'success': False,
            'message': message,
            'error_code': 'NOT_FOUND'
        }, status=404)
    
    context = {
        'error_message': message,
        'request_path': request.path,
        'user': request.user,
    }
    
    return render(request, '404.html', context, status=404)


def handle_validation_error(request, error):
    """
    Handle validation errors.
    
    Args:
        request: Django request object
        error: ValidationError instance
    
    Returns:
        Appropriate response for validation error
    """
    if hasattr(error, 'message_dict'):
        # Field-specific errors
        error_messages = []
        for field, messages in error.message_dict.items():
            for message in messages:
                error_messages.append(f"{field}: {message}")
        message = "; ".join(error_messages)
    else:
        # General validation error
        message = str(error)
    
    if request.headers.get('HX-Request'):
        return JsonResponse({
            'success': False,
            'message': message,
            'error_code': 'VALIDATION_ERROR',
            'errors': getattr(error, 'message_dict', {})
        }, status=400)
    
    messages.error(request, f"Validation error: {message}")
    return redirect(request.META.get('HTTP_REFERER', 'dashboard'))


def handle_smart_supply_error(request, error):
    """
    Handle Smart Supply application-specific errors.
    
    Args:
        request: Django request object
        error: SmartSupplyError instance
    
    Returns:
        Appropriate response for application error
    """
    if request.headers.get('HX-Request'):
        return JsonResponse({
            'success': False,
            'message': error.message,
            'error_code': error.error_code
        }, status=error.status_code)
    
    messages.error(request, error.message)
    return redirect(request.META.get('HTTP_REFERER', 'dashboard'))


def handle_server_error(request, message="An unexpected error occurred"):
    """
    Handle server errors (500).
    
    Args:
        request: Django request object
        message: Error message
    
    Returns:
        Appropriate response for server error
    """
    if request.headers.get('HX-Request'):
        return JsonResponse({
            'success': False,
            'message': "An unexpected error occurred. Please try again.",
            'error_code': 'SERVER_ERROR'
        }, status=500)
    
    context = {
        'error_message': message,
        'request_path': request.path,
        'user': request.user,
        'timestamp': timezone.now(),
    }
    
    return render(request, '500.html', context, status=500)


def safe_get_object_or_404(model, error_message=None, **kwargs):
    """
    Safe version of get_object_or_404 that provides better error messages.
    
    Args:
        model: Django model class
        error_message: Custom error message
        **kwargs: Query parameters
    
    Returns:
        Model instance
    
    Raises:
        Http404: If object not found
    """
    try:
        return model.objects.get(**kwargs)
    except model.DoesNotExist:
        if error_message:
            raise Http404(error_message)
        else:
            model_name = model._meta.verbose_name
            raise Http404(f"{model_name} not found")
    except model.MultipleObjectsReturned:
        model_name = model._meta.verbose_name
        raise Http404(f"Multiple {model_name} objects found")


def validate_gso_access(user):
    """
    Validate that user has GSO access.
    
    Args:
        user: Django User instance
    
    Raises:
        PermissionDenied: If user doesn't have GSO access
    """
    if not user.is_authenticated:
        raise PermissionDenied("Authentication required")
    
    try:
        if user.userprofile.role != 'gso_staff':
            raise PermissionDenied("GSO staff access required")
    except AttributeError:
        raise PermissionDenied("User profile not found")


def validate_admin_access(user):
    """
    Validate that user has admin access.
    
    Args:
        user: Django User instance
    
    Raises:
        PermissionDenied: If user doesn't have admin access
    """
    if not user.is_authenticated:
        raise PermissionDenied("Authentication required")
    
    try:
        if user.userprofile.role != 'admin':
            raise PermissionDenied("Admin access required")
    except AttributeError:
        raise PermissionDenied("User profile not found")


def validate_admin_or_gso_access(user):
    """
    Validate that user has admin or GSO access.
    
    Args:
        user: Django User instance
    
    Raises:
        PermissionDenied: If user doesn't have required access
    """
    if not user.is_authenticated:
        raise PermissionDenied("Authentication required")
    
    try:
        if user.userprofile.role not in ['admin', 'gso_staff']:
            raise PermissionDenied("Admin or GSO staff access required")
    except AttributeError:
        raise PermissionDenied("User profile not found")


def log_error(request, error, context=None):
    """
    Log error with request context.
    
    Args:
        request: Django request object
        error: Exception instance
        context: Additional context data
    """
    log_data = {
        'user': str(request.user) if request.user.is_authenticated else 'Anonymous',
        'path': request.path,
        'method': request.method,
        'error': str(error),
        'error_type': type(error).__name__,
    }
    
    if context:
        log_data.update(context)
    
    logger.error(f"Error in view: {log_data}", exc_info=True)
